import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet, useColorScheme } from 'react-native';
import type { Question } from '../../types/question';

export interface QuestionCardProps {
  question: Question;
  selectedAnswerId: string | null;
  onSelectAnswer: (answerId: string) => void;
}

export function QuestionCard({ question, selectedAnswerId, onSelectAnswer }: QuestionCardProps): JSX.Element {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';

  return (
    <View style={[styles.card, isDark && styles.cardDark]} accessibilityRole="group" accessibilityLabel="Question card">
      <Text style={[styles.questionText, isDark && styles.questionTextDark]} accessibilityRole="header">
        {question.text}
      </Text>
      <View style={styles.answersContainer}>
        {question.answers.map((answer) => {
          const isSelected = selectedAnswerId === answer.id;
          return (
            <TouchableOpacity
              key={answer.id}
              style={[
                styles.answerButton,
                isSelected && (answer.id === question.correctAnswerId ? styles.correctAnswer : styles.incorrectAnswer),
                isDark && styles.answerButtonDark,
              ]}
              onPress={() => onSelectAnswer(answer.id)}
              disabled={!!selectedAnswerId}
              accessibilityRole="button"
              accessibilityState={{ selected: isSelected, disabled: !!selectedAnswerId }}
              accessibilityLabel={`Answer: ${answer.text}`}
            >
              <Text style={[styles.answerText, isDark && styles.answerTextDark]}>{answer.text}</Text>
            </TouchableOpacity>
          );
        })}
      </View>
      {selectedAnswerId && (
        <Text
          style={
            selectedAnswerId === question.correctAnswerId
              ? [styles.feedback, styles.correctFeedback]
              : [styles.feedback, styles.incorrectFeedback]
          }
          accessibilityLiveRegion="polite"
        >
          {selectedAnswerId === question.correctAnswerId ? 'Correct!' : 'Incorrect!'}
        </Text>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  card: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 20,
    width: '100%',
    maxWidth: 400,
    shadowColor: '#000',
    shadowOpacity: 0.1,
    shadowRadius: 8,
    shadowOffset: { width: 0, height: 2 },
    elevation: 2,
  },
  cardDark: {
    backgroundColor: '#222',
  },
  questionText: {
    fontSize: 22,
    fontWeight: 'bold',
    marginBottom: 18,
    color: '#222',
    textAlign: 'center',
  },
  questionTextDark: {
    color: '#fff',
  },
  answersContainer: {
    width: '100%',
  },
  answerButton: {
    backgroundColor: '#f0f0f0',
    padding: 14,
    borderRadius: 8,
    marginBottom: 10,
    borderWidth: 1,
    borderColor: '#ccc',
  },
  answerButtonDark: {
    backgroundColor: '#333',
    borderColor: '#444',
  },
  answerText: {
    fontSize: 18,
    color: '#222',
  },
  answerTextDark: {
    color: '#fff',
  },
  correctAnswer: {
    backgroundColor: '#d4edda',
    borderColor: '#c3e6cb',
  },
  incorrectAnswer: {
    backgroundColor: '#f8d7da',
    borderColor: '#f5c6cb',
  },
  feedback: {
    marginTop: 18,
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  correctFeedback: {
    color: 'green',
  },
  incorrectFeedback: {
    color: 'red',
  },
}); 