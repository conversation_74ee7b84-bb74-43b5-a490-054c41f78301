{"compilerOptions": {"target": "esnext", "module": "esnext", "jsx": "react-native", "strict": true, "moduleResolution": "node", "allowJs": true, "noEmit": true, "isolatedModules": true, "esModuleInterop": true, "skipLibCheck": true, "resolveJsonModule": true, "types": ["react", "react-native"], "forceConsistentCasingInFileNames": true}, "exclude": ["node_modules", "babel.config.js", "metro.config.js", "jest.config.js"], "extends": "expo/tsconfig.base"}