import React, { useReducer } from 'react';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';
import { View, StyleSheet } from 'react-native';
import { QuestionCard } from './components/question-card/QuestionCard';
import type { Question } from './types/question';

const sampleQuestion: Question = {
  text: 'What is the capital of France?',
  answers: [
    { id: 'A', text: 'London' },
    { id: 'B', text: 'Paris' },
    { id: 'C', text: 'Berlin' },
    { id: 'D', text: 'Madrid' },
  ],
  correctAnswerId: 'B',
};

interface State {
  selectedAnswerId: string | null;
}

type Action = { type: 'select'; answerId: string } | { type: 'reset' };

function reducer(state: State, action: Action): State {
  switch (action.type) {
    case 'select':
      return { ...state, selectedAnswerId: action.answerId };
    case 'reset':
      return { selectedAnswerId: null };
    default:
      return state;
  }
}

export default function App(): JSX.Element {
  const [state, dispatch] = useReducer(reducer, { selectedAnswerId: null });

  return (
    <SafeAreaProvider>
      <View style={styles.container}>
        <StatusBar style="auto" />
        <QuestionCard
          question={sampleQuestion}
          selectedAnswerId={state.selectedAnswerId}
          onSelectAnswer={(answerId) => dispatch({ type: 'select', answerId })}
        />
      </View>
    </SafeAreaProvider>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f0f0f0',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
}); 