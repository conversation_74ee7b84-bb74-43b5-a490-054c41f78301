{"name": "reactnative", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"expo": "~53.0.20", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-native": "0.79.5", "react-native-safe-area-context": "5.4.0"}, "devDependencies": {"@babel/core": "^7.20.0", "typescript": "~5.8.3", "@types/react": "~19.0.10"}, "private": true}